config:
  service: common-mailer-api
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: ${DESIRED_COUNT}
  task_config:
    cpu: "256"
    memory: "512"
    containerDefinitions:
      - name: app
        image: ${WSMAILERAPI_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            awslogs-group: "common-mailer-api"
            awslogs-region: "us-east-1"
            awslogs-stream-prefix: "common-mailer-api"
        portMappings:
          - containerPort: 8080 # App Java escucha aquí
        healthCheck:
          command: ["CMD-SHELL", "/healthcheck.sh || exit 1"]
          interval: 30
          timeout: 10
          retries: 3
          startPeriod: 60
        environment:
          - name: SPRING_PROFILES_ACTIVE
            value: Develop
