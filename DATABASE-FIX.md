# Solución al Error de Autenticación MySQL

## Error Actual

```
Cannot create PoolableConnectionFactory (Client does not support authentication protocol requested by server. 
Consider upgrading MariaDB client. plugin was = caching_sha2_password)
```

## Causa del Problema

- **Driver actual:** MariaDB Java Client 2.2.3 (abril 2020)
- **Problema:** Esta versión antigua no soporta el plugin de autenticación `caching_sha2_password` usado por MySQL 8.0+
- **Servidor:** Probablemente estás usando MySQL 8.0 o superior

## Soluciones

### ✅ **Solución 1: Cambiar el Método de Autenticación en MySQL (RECOMENDADO)**

Esta es la solución más rápida y no requiere recompilar la aplicación.

#### Paso 1: Conectarse al servidor MySQL

```bash
mysql -u root -p
```

#### Paso 2: Cambiar el método de autenticación del usuario

```sql
-- Ver el método de autenticación actual
SELECT user, host, plugin FROM mysql.user WHERE user = 'tu_usuario';

-- Cambiar a mysql_native_password
ALTER USER 'tu_usuario'@'%' IDENTIFIED WITH mysql_native_password BY 'tu_password';
FLUSH PRIVILEGES;
```

**Reemplaza:**
- `tu_usuario` con el nombre de usuario que usa la aplicación
- `tu_password` con la contraseña actual del usuario
- `%` con el host específico si es necesario (ej: `localhost`, `10.0.%`, etc.)

#### Paso 3: Verificar el cambio

```sql
SELECT user, host, plugin FROM mysql.user WHERE user = 'tu_usuario';
```

Deberías ver `mysql_native_password` en la columna `plugin`.

#### Paso 4: Reiniciar la aplicación

```bash
# Si estás usando docker-compose localmente
docker-compose restart

# Si está en Fargate, hacer un nuevo despliegue
git commit --allow-empty -m "Trigger redeploy after DB auth fix"
git push
```

---

### 🔧 **Solución 2: Actualizar el Driver MariaDB (Requiere Recompilar)**

Si tienes acceso al código fuente y puedes recompilar:

#### Maven (pom.xml)

```xml
<dependency>
    <groupId>org.mariadb.jdbc</groupId>
    <artifactId>mariadb-java-client</artifactId>
    <version>3.3.0</version>
</dependency>
```

#### Gradle (build.gradle)

```gradle
implementation 'org.mariadb.jdbc:mariadb-java-client:3.3.0'
```

**Ventajas:**
- Soporte completo para `caching_sha2_password`
- Mejor rendimiento
- Correcciones de seguridad

**Desventajas:**
- Requiere acceso al código fuente
- Requiere recompilar y generar nuevo WAR

---

### 🔄 **Solución 3: Usar MySQL Connector en lugar de MariaDB**

Si tienes acceso al código fuente, puedes cambiar al driver oficial de MySQL:

#### Maven (pom.xml)

```xml
<!-- Remover MariaDB -->
<!-- <dependency>
    <groupId>org.mariadb.jdbc</groupId>
    <artifactId>mariadb-java-client</artifactId>
    <version>2.2.3</version>
</dependency> -->

<!-- Agregar MySQL Connector -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <version>8.2.0</version>
</dependency>
```

#### Actualizar config.properties

```properties
# Cambiar el driver
env.db.mysql.driver=com.mysql.cj.jdbc.Driver

# Cambiar la URL
env.db.mysql.url=jdbc:mysql://${DB_MYSQL_URL}:3306/global_services?connectTimeout=3000&socketTimeout=20000&allowPublicKeyRetrieval=true&useSSL=false
```

---

## Configuración Actual del Proyecto

### ✅ Driver Actualizado en el WAR

```
WEB-INF/lib/mariadb-java-client-3.3.3.jar (ACTUALIZADO - Febrero 2024)
```

**Cambio realizado:**
- ❌ Driver antiguo: `mariadb-java-client-2.2.3.jar` (Abril 2020)
- ✅ Driver nuevo: `mariadb-java-client-3.3.3.jar` (Febrero 2024)

**Beneficios:**
- ✅ Soporte completo para `caching_sha2_password` (MySQL 8.0+)
- ✅ Mejor rendimiento y estabilidad
- ✅ Correcciones de seguridad
- ✅ Compatible con MySQL 5.x, 8.x y MariaDB 10.x

**Backup:**
- El WAR original se guardó como `wsmailer.war.backup`

### Configuración Actual (config.properties.template)

```properties
env.db.mysql.driver=org.mariadb.jdbc.Driver
env.db.mysql.url=jdbc:mariadb://${DB_MYSQL_URL}:3306/global_services?connectTimeout=3000&socketTimeout=20000&allowPublicKeyRetrieval=true&useSSL=false
```

**Nota:** Ya agregué el parámetro `allowPublicKeyRetrieval=true` que puede ayudar con algunos problemas de autenticación.

---

## Verificación Post-Fix

### 1. Verificar la conexión localmente

```bash
# Desde el contenedor Docker
docker-compose exec wsmailer bash

# Probar conexión con el driver
java -cp /home/<USER>/app/wsmailer.war org.mariadb.jdbc.Driver \
  "***********************************************" \
  "usuario" "password"
```

### 2. Verificar los logs de la aplicación

```bash
# Logs del contenedor
docker-compose logs -f wsmailer

# O en Fargate
aws logs tail /aws/ecs/common-mailer-api --follow
```

Busca mensajes como:
- ✅ `Connection established` o similar
- ❌ `Cannot create PoolableConnectionFactory`

### 3. Probar un endpoint de la aplicación

```bash
curl http://localhost:8080/tu-endpoint
```

---

## Información Adicional

### ¿Qué versión de MySQL/MariaDB estás usando?

```sql
SELECT VERSION();
```

- **MySQL 8.0+**: Usa `caching_sha2_password` por defecto → Necesitas Solución 1 o 2
- **MySQL 5.7**: Usa `mysql_native_password` por defecto → Debería funcionar
- **MariaDB 10.x**: Usa `mysql_native_password` por defecto → Debería funcionar

### Parámetros de Conexión Útiles

```properties
# Parámetros ya incluidos
connectTimeout=3000          # Timeout de conexión (3 segundos)
socketTimeout=20000          # Timeout de socket (20 segundos)
allowPublicKeyRetrieval=true # Permite recuperar la clave pública del servidor
useSSL=false                 # Desactiva SSL (activar en producción si es posible)

# Parámetros adicionales opcionales
autoReconnect=true           # Reconectar automáticamente
maxReconnects=3              # Número máximo de reintentos
```

---

## Recomendación Final

**Para resolver rápidamente:** Usa la **Solución 1** (cambiar autenticación en MySQL)

**Para solución a largo plazo:** Usa la **Solución 2** (actualizar driver a 3.3.0)

---

## Comandos Rápidos

### Cambiar autenticación del usuario en MySQL

```sql
ALTER USER 'wsmailer_user'@'%' IDENTIFIED WITH mysql_native_password BY 'tu_password';
FLUSH PRIVILEGES;
```

### Verificar el cambio

```sql
SELECT user, host, plugin FROM mysql.user WHERE user = 'wsmailer_user';
```

### Reiniciar aplicación

```bash
docker-compose restart
```

