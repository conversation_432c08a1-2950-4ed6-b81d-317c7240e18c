FROM openjdk:8-jdk-alpine

# Instalar dependencias necesarias
RUN apk update && apk add --no-cache \
    curl \
    bash \
    tzdata

# Configurar zona horaria
ENV TZ=America/Santiago

# Crear usuario y estructura de carpetas
RUN addgroup -S appmobid && adduser -S appmobid -G appmobid && \
    mkdir -p /home/<USER>/app && \
    mkdir -p /home/<USER>/properties-docker/wsmailer/properties/1.0.0 && \
    mkdir -p /home/<USER>/properties-docker/ws-enterprise-cl/properties/1.0.0 && \
    chown -R appmobid:appmobid /home/<USER>

# Copiar WAR
COPY ./wsmailer.war /home/<USER>/app/wsmailer.war

# Copiar archivos de propiedades
COPY ./properties/application.properties /home/<USER>/properties-docker/wsmailer/properties/1.0.0/
COPY ./properties/log4j2.properties      /home/<USER>/properties-docker/wsmailer/properties/1.0.0/
COPY ./properties/msg.properties         /home/<USER>/properties-docker/wsmailer/properties/

# Copiar config.properties si existe, o el template
COPY ./properties/config.properties* /home/<USER>/properties-docker/wsmailer/properties/

# Ajustar permisos de configuración
RUN chown -R appmobid:appmobid /home/<USER>
    chmod -R 644 /home/<USER>/properties-docker/wsmailer/properties/1.0.0/

# Copiar script de arranque
COPY start.sh /start.sh
RUN chmod +x /start.sh

# Cambiar al usuario no root
USER appmobid
WORKDIR /home/<USER>

# Exponer puerto de la aplicación Java
EXPOSE 8080

# Ejecutar aplicación
CMD ["/bin/sh", "/start.sh"]
