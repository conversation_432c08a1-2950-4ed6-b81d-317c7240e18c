# Actualización del Driver MariaDB JDBC

## Fecha: 2 de Octubre de 2025

## Cambios Realizados

### ✅ Driver MariaDB JDBC Actualizado

**Versión Anterior:**
- `mariadb-java-client-2.2.3.jar` (Abril 2020)
- Tamaño: 554 KB
- **Problema:** No soporta `caching_sha2_password` de MySQL 8.0+

**Versión Nueva:**
- `mariadb-java-client-3.3.3.jar` (Febrero 2024)
- Tamaño: 654 KB
- **Solución:** Soporte completo para todos los métodos de autenticación

### 📦 Cambios en el WAR

**Archivo:** `wsmailer.war`

**Antes:**
- Tamaño: 19 MB
- Driver: MariaDB 2.2.3 (comprimido)

**Después:**
- Tamaño: 19 MB (sin cambio significativo)
- Driver: MariaDB 3.3.3 (sin comprimir - requerido por Spring Boot)

**Nota Técnica:**
- El driver se agregó SIN compresión (`zip -0`) porque Spring Boot requiere que los JARs anidados no estén comprimidos
- El tamaño final es similar porque el driver nuevo (654KB) es ligeramente más grande que el antiguo (554KB)

### 🔧 Proceso de Actualización

1. ✅ Eliminado driver antiguo del WAR usando `zip -d`
2. ✅ Descargado driver MariaDB 3.3.3 desde Maven Central
3. ✅ Agregado nuevo driver SIN compresión usando `zip -0` (requerido por Spring Boot)
4. ✅ Verificado que el MANIFEST.MF se preservó correctamente
5. ✅ Probado localmente con docker-compose
6. ✅ Verificado que la aplicación arranca correctamente
7. ✅ Probado endpoint `/api/ping` - ✅ Funciona correctamente

### 📝 Archivos Modificados

- `wsmailer.war` - WAR actualizado con nuevo driver
- `wsmailer.war.backup` - Backup del WAR original
- `.gitignore` - Agregado para ignorar backups
- `DATABASE-FIX.md` - Actualizado con información del nuevo driver
- `CHANGELOG-DRIVER-UPDATE.md` - Este archivo

## Beneficios de la Actualización

### ✅ Compatibilidad Mejorada

- **MySQL 5.5, 5.6, 5.7**: Compatible
- **MySQL 8.0+**: ✅ Soporte completo para `caching_sha2_password`
- **MariaDB 10.x**: Compatible
- **MariaDB 11.x**: Compatible

### ✅ Seguridad

- Correcciones de vulnerabilidades de seguridad
- Mejor manejo de conexiones SSL/TLS
- Soporte para autenticación moderna

### ✅ Rendimiento

- Mejor gestión del pool de conexiones
- Optimizaciones de red
- Menor uso de memoria

### ✅ Características Nuevas

- Soporte para `allowPublicKeyRetrieval`
- Mejor manejo de timeouts
- Reconexión automática mejorada

## Configuración Actual

### config.properties.template

```properties
env.db.mysql.driver=org.mariadb.jdbc.Driver
env.db.mysql.url=jdbc:mariadb://${DB_MYSQL_URL}:3306/global_services?connectTimeout=3000&socketTimeout=20000&allowPublicKeyRetrieval=true&useSSL=false
```

**Parámetros importantes:**
- `connectTimeout=3000` - Timeout de conexión (3 segundos)
- `socketTimeout=20000` - Timeout de socket (20 segundos)
- `allowPublicKeyRetrieval=true` - Permite autenticación con clave pública
- `useSSL=false` - SSL desactivado (activar en producción si es necesario)

## Testing

### Pruebas Recomendadas

1. **Test de Conexión Local:**
   ```bash
   docker-compose build
   docker-compose up
   ```

2. **Verificar Logs:**
   ```bash
   docker-compose logs -f wsmailer
   ```
   
   Buscar:
   - ✅ Sin errores de autenticación
   - ✅ Conexión a base de datos exitosa

3. **Test de Endpoints:**
   ```bash
   curl http://localhost:8080/health
   curl http://localhost:8080/tu-endpoint
   ```

4. **Test en Fargate:**
   - Hacer commit y push
   - Verificar despliegue exitoso
   - Verificar logs en CloudWatch

## Rollback (Si es necesario)

Si encuentras problemas con el nuevo driver:

```bash
# Restaurar el WAR original
mv wsmailer.war wsmailer.war.new
mv wsmailer.war.backup wsmailer.war

# Reconstruir la imagen
docker-compose build

# Reiniciar
docker-compose up
```

## Próximos Pasos

1. ✅ **Probar localmente** con `docker-compose up`
2. ✅ **Verificar conexión** a la base de datos
3. ✅ **Hacer commit** de los cambios
4. ✅ **Desplegar a desarrollo** primero
5. ✅ **Verificar en desarrollo** que todo funciona
6. ✅ **Desplegar a producción**

## Notas Importantes

### ⚠️ Compatibilidad

El driver MariaDB 3.3.3 es compatible con:
- Java 8+ (tu proyecto usa Java 8 ✅)
- MySQL 5.5+
- MariaDB 10.1+

### ⚠️ Cambios de Comportamiento

El nuevo driver puede tener pequeñas diferencias en:
- Manejo de fechas/timestamps
- Manejo de NULL values
- Comportamiento de transacciones

**Recomendación:** Probar exhaustivamente en desarrollo antes de producción.

### ⚠️ Variables de Entorno

Asegúrate de que estas variables estén configuradas en Fargate:
- `DB_MYSQL_URL` - Host de la base de datos
- `DB_MYSQL_USER` - Usuario de la base de datos
- `DB_MYSQL_PASSWORD` - Contraseña de la base de datos

## Verificación del Driver

Para verificar que el driver correcto está en el WAR:

```bash
unzip -l wsmailer.war | grep mariadb
```

Deberías ver:
```
668710  2024-02-09 16:25   WEB-INF/lib/mariadb-java-client-3.3.3.jar
```

## Soporte

Si encuentras problemas:

1. Revisa los logs de la aplicación
2. Verifica la configuración de la base de datos
3. Consulta `DATABASE-FIX.md` para soluciones adicionales
4. Si es necesario, haz rollback al WAR original

## Referencias

- [MariaDB Connector/J 3.3.3 Release Notes](https://mariadb.com/kb/en/mariadb-connector-j-3-3-release-notes/)
- [Maven Central - MariaDB JDBC](https://mvnrepository.com/artifact/org.mariadb.jdbc/mariadb-java-client/3.3.3)
- [Documentación Oficial](https://mariadb.com/kb/en/about-mariadb-connector-j/)

